#!/bin/bash

# Function to check if a command was successful
check_error() {
    if [ $? -ne 0 ]; then
        echo "Error: $1"
        exit 1
    fi
}

# Function to check if script is run as root
check_root() {
    if [ "$EUID" -eq 0 ]; then
        echo "Please do not run this script as root/sudo. It will ask for elevation when needed."
        exit 1
    fi
}

# Function to check system compatibility
check_system() {
    if ! command -v lsb_release &> /dev/null; then
        echo "Error: This script requires lsb-release to be installed"
        exit 1
    fi

    if [ "$(lsb_release -si)" != "Ubuntu" ]; then
        echo "Error: This script is designed for Ubuntu only"
        exit 1
    fi
}

# Main installation function
install_docker() {
    echo "Starting Docker installation..."

    # Update package index and install prerequisites
    echo "Installing prerequisites..."
    sudo apt-get update
    check_error "Failed to update package index"
    
    sudo apt-get install -y ca-certificates curl
    check_error "Failed to install prerequisites"

    # Set up Docker repository
    echo "Setting up Docker repository..."
    sudo install -m 0755 -d /etc/apt/keyrings
    check_error "Failed to create keyrings directory"

    sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg -o /etc/apt/keyrings/docker.asc
    check_error "Failed to download Docker GPG key"

    sudo chmod a+r /etc/apt/keyrings/docker.asc
    check_error "Failed to set permissions on Docker GPG key"

    # Add Docker repository
    echo "Adding Docker repository to Apt sources..."
    echo \
    "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] https://download.docker.com/linux/ubuntu \
    $(. /etc/os-release && echo "$VERSION_CODENAME") stable" | \
    sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    check_error "Failed to add Docker repository"

    # Update package index with Docker repository
    sudo apt-get update
    check_error "Failed to update package index with Docker repository"

    # Install Docker packages
    echo "Installing Docker packages..."
    sudo apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    check_error "Failed to install Docker packages"

    # Add user to docker group
    echo "Adding user to docker group..."
    sudo usermod -aG docker $USER
    check_error "Failed to add user to docker group"

    echo "Docker installation completed successfully!"
    echo "Please log out and log back in for group changes to take effect."
    echo "Alternatively, run 'newgrp docker' to activate changes for current shell."
}

# Main script execution
main() {
    check_root
    check_system
    install_docker
}

main